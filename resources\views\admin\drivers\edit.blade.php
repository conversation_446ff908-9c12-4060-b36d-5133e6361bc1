@extends('layouts.admin_minimal')

@section('title', 'Modifier le chauffeur')

@section('content')
<div class="container-fluid py-4">
    <!-- Header avec breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.drivers.index') }}" class="text-decoration-none">
                            <i class="fas fa-users me-1"></i> Chauffeurs
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Modifier chauffeur</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-edit text-primary me-2"></i>
                Modifier le Chauffeur
            </h1>
            <p class="text-muted mb-0">Modifiez les informations de {{ $driver->first_name }} {{ $driver->last_name }}</p>
        </div>
        <div>
            <a href="{{ route('admin.drivers.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
            </a>
        </div>
    </div>

    <!-- Carte principale du formulaire -->
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <div class="card shadow-lg border-0 rounded-3 overflow-hidden">
                <!-- En-tête de la carte avec gradient -->
                <div class="card-header bg-gradient-primary text-white py-4">
                    <div class="d-flex align-items-center">
                        <div class="avatar-preview me-3">
                            <div class="avatar-circle bg-white text-primary" id="avatarPreview">
                                {{ strtoupper(substr($driver->first_name, 0, 1) . substr($driver->last_name, 0, 1)) }}
                            </div>
                        </div>
                        <div>
                            <h4 class="mb-1">
                                <span id="fullNamePreview">{{ $driver->first_name }} {{ $driver->last_name }}</span>
                            </h4>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-envelope me-1"></i> {{ $driver->email }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <form id="driverForm" action="{{ route('admin.drivers.update', $driver->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Section Informations personnelles -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="section-title">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    Informations Personnelles
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control form-control-lg @error('first_name') is-invalid @enderror"
                                               id="first_name"
                                               name="first_name"
                                               value="{{ old('first_name', $driver->first_name) }}"
                                               placeholder="Prénom"
                                               required>
                                        <label for="first_name">
                                            <i class="fas fa-user me-1"></i> Prénom <span class="text-danger">*</span>
                                        </label>
                                        @error('first_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control form-control-lg @error('last_name') is-invalid @enderror"
                                               id="last_name"
                                               name="last_name"
                                               value="{{ old('last_name', $driver->last_name) }}"
                                               placeholder="Nom"
                                               required>
                                        <label for="last_name">
                                            <i class="fas fa-user me-1"></i> Nom <span class="text-danger">*</span>
                                        </label>
                                        @error('last_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Contact -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="section-title">
                                    <i class="fas fa-address-book text-info me-2"></i>
                                    Informations de Contact
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email"
                                               class="form-control form-control-lg @error('email') is-invalid @enderror"
                                               id="email"
                                               name="email"
                                               value="{{ old('email', $driver->email) }}"
                                               placeholder="Email"
                                               required>
                                        <label for="email">
                                            <i class="fas fa-envelope me-1"></i> Email <span class="text-danger">*</span>
                                        </label>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control form-control-lg @error('phone') is-invalid @enderror"
                                               id="phone"
                                               name="phone"
                                               value="{{ old('phone', $driver->phone) }}"
                                               placeholder="Téléphone"
                                               required>
                                        <label for="phone">
                                            <i class="fas fa-phone me-1"></i> Téléphone <span class="text-danger">*</span>
                                        </label>
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row g-4 mt-2">
                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control @error('address') is-invalid @enderror"
                                                  id="address"
                                                  name="address"
                                                  placeholder="Adresse complète"
                                                  style="height: 100px;">{{ old('address', $driver->address) }}</textarea>
                                        <label for="address">
                                            <i class="fas fa-map-marker-alt me-1"></i> Adresse
                                        </label>
                                        @error('address')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        <!-- Section Permis de conduire -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="section-title">
                                    <i class="fas fa-id-card text-warning me-2"></i>
                                    Permis de Conduire
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control form-control-lg @error('license_number') is-invalid @enderror"
                                               id="license_number"
                                               name="license_number"
                                               value="{{ old('license_number', $driver->license_number) }}"
                                               placeholder="Numéro de permis"
                                               required>
                                        <label for="license_number">
                                            <i class="fas fa-id-card me-1"></i> Numéro de Permis <span class="text-danger">*</span>
                                        </label>
                                        @error('license_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="date"
                                               class="form-control form-control-lg @error('license_expiry') is-invalid @enderror"
                                               id="license_expiry"
                                               name="license_expiry"
                                               value="{{ old('license_expiry', $driver->license_expiry->format('Y-m-d')) }}"
                                               required>
                                        <label for="license_expiry">
                                            <i class="fas fa-calendar-alt me-1"></i> Date d'Expiration <span class="text-danger">*</span>
                                        </label>
                                        @error('license_expiry')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Alerte de validité du permis -->
                            <div class="mt-3" id="licenseValidityAlert" style="display: none;">
                                <div class="alert border-0 rounded-3" id="licenseValidityText">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <div>
                                            <strong>Attention !</strong>
                                            <span id="licenseValidityDetails"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Statut -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="section-title">
                                    <i class="fas fa-toggle-on text-success me-2"></i>
                                    Statut du Chauffeur
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="status-options">
                                <div class="form-check-card">
                                    <input type="radio"
                                           class="form-check-input"
                                           id="status_available"
                                           name="status"
                                           value="available"
                                           {{ old('status', $driver->status) == 'available' ? 'checked' : '' }}>
                                    <label class="status-card" for="status_available">
                                        <div class="d-flex align-items-center">
                                            <div class="status-icon bg-success text-white me-3">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">Disponible</h6>
                                                <p class="text-muted mb-0 small">Le chauffeur est disponible pour de nouvelles missions</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <div class="form-check-card">
                                    <input type="radio"
                                           class="form-check-input"
                                           id="status_unavailable"
                                           name="status"
                                           value="unavailable"
                                           {{ old('status', $driver->status) == 'unavailable' ? 'checked' : '' }}>
                                    <label class="status-card" for="status_unavailable">
                                        <div class="d-flex align-items-center">
                                            <div class="status-icon bg-danger text-white me-3">
                                                <i class="fas fa-times-circle"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">Indisponible</h6>
                                                <p class="text-muted mb-0 small">Le chauffeur n'est pas disponible actuellement</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            @error('status')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Section Notes -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="section-title">
                                    <i class="fas fa-sticky-note text-secondary me-2"></i>
                                    Notes Additionnelles
                                </h5>
                                <div class="section-divider"></div>
                            </div>

                            <div class="form-floating">
                                <textarea class="form-control @error('notes') is-invalid @enderror"
                                          id="notes"
                                          name="notes"
                                          placeholder="Notes sur le chauffeur..."
                                          style="height: 120px;">{{ old('notes', $driver->notes) }}</textarea>
                                <label for="notes">
                                    <i class="fas fa-sticky-note me-1"></i> Notes (optionnel)
                                </label>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" class="btn btn-outline-secondary" id="previewBtn">
                                    <i class="fas fa-eye me-1"></i> Aperçu
                                </button>
                                <button type="button" class="btn btn-outline-info" id="resetBtn">
                                    <i class="fas fa-undo me-1"></i> Réinitialiser
                                </button>
                            </div>
                            <div>
                                <a href="{{ route('admin.drivers.index') }}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-save me-1"></i>
                                    <span class="btn-text">Enregistrer les Modifications</span>
                                    <span class="spinner-border spinner-border-sm ms-2" role="status" style="display: none;"></span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Résumé du formulaire (masqué par défaut) -->
    <div class="row justify-content-center mt-4" id="formSummary" style="display: none;">
        <div class="col-12 col-xl-10">
            <div class="card border-left-info shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-1"></i> Aperçu des Modifications
                    </h6>
                </div>
                <div class="card-body" id="summaryContent">
                    <!-- Le contenu sera généré par JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Styles généraux */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .bg-gradient-info {
        background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
    }
    .bg-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    .bg-gradient-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Bordures colorées */
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }
    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }
    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }

    /* Sections du formulaire */
    .form-section {
        position: relative;
        animation: slideInUp 0.6s ease-out;
    }

    .section-header {
        position: relative;
        margin-bottom: 2rem;
    }

    .section-title {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .section-divider {
        height: 2px;
        background: linear-gradient(90deg, #e2e8f0 0%, transparent 100%);
        border-radius: 1px;
    }

    /* Amélioration des champs de formulaire */
    .form-floating > .form-control,
    .form-floating > .form-select {
        border: 2px solid #e2e8f0;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    .form-control-lg, .form-select-lg {
        padding: 1rem 1.25rem;
        font-size: 1.1rem;
    }

    /* Labels améliorés */
    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.75rem;
    }

    /* Avatar preview */
    .avatar-preview .avatar-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1rem;
        text-transform: uppercase;
    }

    /* Cartes de statut radio */
    .status-options {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    .form-check-card {
        margin-bottom: 0 !important;
    }
    .form-check-input {
        display: none;
    }
    .status-card {
        display: block;
        padding: 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    .status-card:hover {
        border-color: #cbd5e0;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .form-check-input:checked + .status-card {
        border-color: #4e73df;
        background: #f7fafc;
        box-shadow: 0 4px 12px rgba(78, 115, 223, 0.15);
    }
    .status-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    /* Animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .card-body {
            padding: 1.5rem;
        }
        .status-options {
            gap: 0.5rem;
        }
        .status-card {
            padding: 0.75rem;
        }
        .status-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 30px;
        }
        .avatar-preview .avatar-circle {
            width: 40px;
            height: 40px;
            font-size: 0.875rem;
        }
    }

    /* Indicateur de chargement */
    .btn .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Textarea amélioré */
    textarea.form-control {
        resize: vertical;
        min-height: 120px;
    }

    /* Alertes personnalisées */
    .alert {
        border-radius: 0.75rem;
        border: none;
    }

    /* Breadcrumb personnalisé */
    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }
    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
    }
    .breadcrumb-item a {
        color: #4e73df;
        text-decoration: none;
    }
    .breadcrumb-item a:hover {
        color: #2e59d9;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('driverForm');
    const submitBtn = document.getElementById('submitBtn');
    const previewBtn = document.getElementById('previewBtn');
    const resetBtn = document.getElementById('resetBtn');
    const formSummary = document.getElementById('formSummary');
    const summaryContent = document.getElementById('summaryContent');
    const fullNamePreview = document.getElementById('fullNamePreview');
    const avatarPreview = document.getElementById('avatarPreview');
    const licenseValidityAlert = document.getElementById('licenseValidityAlert');
    const licenseValidityText = document.getElementById('licenseValidityText');
    const licenseValidityDetails = document.getElementById('licenseValidityDetails');

    // Validation en temps réel
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            validateField(this);
            updateNamePreview();
            updateLicenseValidity();
            updateFormSummary();
        });
        input.addEventListener('change', function() {
            validateField(this);
            updateNamePreview();
            updateLicenseValidity();
            updateFormSummary();
        });
    });

    // Validation d'un champ
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');

        // Retirer les classes d'erreur précédentes
        field.classList.remove('is-invalid', 'is-valid');

        if (isRequired && !value) {
            field.classList.add('is-invalid');
            return false;
        }

        // Validation spécifique par type
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                field.classList.add('is-invalid');
                return false;
            }
        }

        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\d\s\-\+\(\)]+$/;
            if (!phoneRegex.test(value)) {
                field.classList.add('is-invalid');
                return false;
            }
        }

        if (value) {
            field.classList.add('is-valid');
        }

        return true;
    }

    // Mise à jour de l'aperçu du nom
    function updateNamePreview() {
        const firstName = document.getElementById('first_name').value.trim();
        const lastName = document.getElementById('last_name').value.trim();

        if (firstName || lastName) {
            fullNamePreview.textContent = `${firstName} ${lastName}`.trim();

            // Mise à jour de l'avatar
            const initials = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
            avatarPreview.textContent = initials || 'CH';
        }
    }

    // Vérification de la validité du permis
    function updateLicenseValidity() {
        const licenseExpiry = document.getElementById('license_expiry').value;

        if (licenseExpiry) {
            const expiryDate = new Date(licenseExpiry);
            const today = new Date();
            const diffTime = expiryDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            licenseValidityAlert.style.display = 'block';

            if (diffDays < 0) {
                licenseValidityText.className = 'alert alert-danger border-0 rounded-3';
                licenseValidityDetails.textContent = `Le permis a expiré il y a ${Math.abs(diffDays)} jour(s).`;
            } else if (diffDays <= 30) {
                licenseValidityText.className = 'alert alert-warning border-0 rounded-3';
                licenseValidityDetails.textContent = `Le permis expire dans ${diffDays} jour(s).`;
            } else {
                licenseValidityText.className = 'alert alert-success border-0 rounded-3';
                licenseValidityDetails.textContent = `Le permis est valide pour encore ${diffDays} jour(s).`;
            }
        } else {
            licenseValidityAlert.style.display = 'none';
        }
    }

    // Mise à jour du résumé du formulaire
    function updateFormSummary() {
        const firstName = document.getElementById('first_name').value.trim();
        const lastName = document.getElementById('last_name').value.trim();
        const email = document.getElementById('email').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const licenseNumber = document.getElementById('license_number').value.trim();
        const status = document.querySelector('input[name="status"]:checked')?.value;

        if (firstName && lastName && email) {
            const statusText = status === 'available' ? 'Disponible' : 'Indisponible';
            const statusClass = status === 'available' ? 'text-success' : 'text-danger';

            summaryContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Nom complet :</strong> ${firstName} ${lastName}</p>
                        <p><strong>Email :</strong> ${email}</p>
                        <p><strong>Téléphone :</strong> ${phone}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>N° Permis :</strong> ${licenseNumber}</p>
                        <p><strong>Statut :</strong> <span class="${statusClass}">${statusText}</span></p>
                    </div>
                </div>
            `;
        }
    }

    // Bouton aperçu
    previewBtn.addEventListener('click', function() {
        updateFormSummary();
        if (formSummary.style.display === 'none') {
            formSummary.style.display = 'block';
            formSummary.scrollIntoView({ behavior: 'smooth' });
            this.innerHTML = '<i class="fas fa-eye-slash me-1"></i> Masquer l\'aperçu';
        } else {
            formSummary.style.display = 'none';
            this.innerHTML = '<i class="fas fa-eye me-1"></i> Aperçu';
        }
    });

    // Bouton réinitialiser
    resetBtn.addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
            form.reset();
            form.querySelectorAll('.is-invalid, .is-valid').forEach(el => {
                el.classList.remove('is-invalid', 'is-valid');
            });
            formSummary.style.display = 'none';
            previewBtn.innerHTML = '<i class="fas fa-eye me-1"></i> Aperçu';
            updateNamePreview();
            updateLicenseValidity();
        }
    });

    // Définir la date minimale pour l'expiration du permis
    const licenseExpiryInput = document.getElementById('license_expiry');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    licenseExpiryInput.min = tomorrow.toISOString().split('T')[0];

    // Soumission du formulaire
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Afficher le spinner
        const spinner = submitBtn.querySelector('.spinner-border');
        const btnText = submitBtn.querySelector('.btn-text');
        spinner.style.display = 'inline-block';
        btnText.textContent = 'Enregistrement...';
        submitBtn.disabled = true;

        // Reset previous error states
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        const formData = new FormData(form);
        const data = {};
        formData.forEach((value, key) => {
            if (key !== '_token' && key !== '_method') {
                data[key] = value;
            }
        });

        try {
            const response = await fetch('{{ route("admin.drivers.update", $driver->id) }}', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!response.ok) {
                if (response.status === 422) {
                    // Validation errors
                    Object.keys(result.errors).forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        if (input) {
                            input.classList.add('is-invalid');
                            // Créer ou mettre à jour le message d'erreur
                            let feedback = input.parentNode.querySelector('.invalid-feedback');
                            if (!feedback) {
                                feedback = document.createElement('div');
                                feedback.className = 'invalid-feedback';
                                input.parentNode.appendChild(feedback);
                            }
                            feedback.textContent = result.errors[field][0];
                        }
                    });

                    // Faire défiler vers la première erreur
                    const firstError = form.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                } else {
                    throw new Error(result.message || 'Une erreur est survenue');
                }
                return;
            }

            // Success
            btnText.textContent = 'Modifications enregistrées !';
            submitBtn.classList.remove('btn-primary');
            submitBtn.classList.add('btn-success');

            // Afficher une notification de succès
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'Succès !',
                    text: result.message || 'Les modifications ont été enregistrées avec succès.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }

            // Redirect after 2 seconds
            setTimeout(() => {
                window.location.href = '{{ route("admin.drivers.index") }}';
            }, 2000);

        } catch (error) {
            console.error('Error:', error);

            // Afficher une notification d'erreur
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur !',
                    text: error.message || 'Une erreur est survenue lors de l\'enregistrement.',
                });
            }
        } finally {
            // Masquer le spinner
            spinner.style.display = 'none';
            btnText.textContent = 'Enregistrer les Modifications';
            submitBtn.disabled = false;
        }
    });

    // Initialisation
    updateNamePreview();
    updateLicenseValidity();
    updateFormSummary();
});
</script>
@endpush
