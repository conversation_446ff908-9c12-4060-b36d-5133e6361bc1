<?php $__env->startSection('title', 'Gestion des Utilisateurs'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header moderne avec statistiques -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users text-primary me-2"></i>
                Gestion des Utilisateurs
            </h1>
            <p class="text-muted mb-0">Gérez les comptes utilisateurs et leurs permissions</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" id="toggleView" title="Changer la vue">
                <i class="fas fa-th-large" id="viewIcon"></i>
            </button>
            <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nouvel Utilisateur
            </a>
        </div>
    </div>

    <!-- Cartes de statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Utilisateurs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($users->total()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Utilisateurs Actifs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($users->where('is_active', true)->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Utilisateurs Inactifs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($users->where('is_active', false)->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-slash fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Rôles Différents
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php
                                    $uniqueRoles = collect();
                                    foreach($users as $user) {
                                        foreach($user->roles as $role) {
                                            $uniqueRoles->push($role->name);
                                        }
                                    }
                                    $uniqueRolesCount = $uniqueRoles->unique()->count();
                                ?>
                                <?php echo e($uniqueRolesCount); ?>

                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tag fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche avancés -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Filtres et Recherche
            </h6>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="searchInput" placeholder="Rechercher...">
                        <label for="searchInput">
                            <i class="fas fa-search me-1"></i> Rechercher par nom ou email
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="statusFilter">
                            <option value="">Tous les statuts</option>
                            <option value="active">Actifs</option>
                            <option value="inactive">Inactifs</option>
                        </select>
                        <label for="statusFilter">
                            <i class="fas fa-toggle-on me-1"></i> Filtrer par statut
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="roleFilter">
                            <option value="">Tous les rôles</option>
                            <option value="admin">Administrateur</option>
                            <option value="accountant">Comptable</option>
                            <option value="cement_manager">Gestionnaire Ciment</option>
                            <option value="iron_manager">Gestionnaire Fer</option>
                            <option value="cashier">Caissier</option>
                            <option value="customer_service">Service Client</option>
                        </select>
                        <label for="roleFilter">
                            <i class="fas fa-user-tag me-1"></i> Filtrer par rôle
                        </label>
                    </div>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100 h-100" id="clearFilters">
                        <i class="fas fa-times me-1"></i> Effacer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue en cartes (par défaut) -->
    <div id="cardView" class="users-view">
        <div class="row" id="usersContainer">
            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xl-4 col-lg-6 col-md-6 mb-4 user-item"
                 data-status="<?php echo e($user->is_active ? 'active' : 'inactive'); ?>"
                 data-roles="<?php echo e($user->roles->pluck('name')->implode(',')); ?>"
                 data-search="<?php echo e(strtolower($user->name . ' ' . $user->email)); ?>">
                <div class="card user-card shadow-sm border-0 h-100">
                    <!-- En-tête de la carte avec fond bleu -->
                    <div class="card-header bg-primary text-white py-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-circle me-3">
                                <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 font-weight-bold"><?php echo e($user->name); ?></h6>
                                <p class="mb-0 opacity-75 small">
                                    <i class="fas fa-envelope me-1"></i> <?php echo e($user->email); ?>

                                </p>
                            </div>
                            <div class="status-indicator">
                                <?php if($user->is_active): ?>
                                    <i class="fas fa-circle text-success" title="Actif"></i>
                                <?php else: ?>
                                    <i class="fas fa-circle text-danger" title="Inactif"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="card-body p-3">
                        <!-- Rôles -->
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-user-tag me-1"></i> Rôles
                            </h6>
                            <div class="d-flex flex-wrap gap-1">
                                <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $roleColors = [
                                            'admin' => 'bg-danger',
                                            'accountant' => 'bg-primary',
                                            'cement_manager' => 'bg-success',
                                            'iron_manager' => 'bg-info',
                                            'cashier' => 'bg-warning text-dark',
                                            'customer_service' => 'bg-secondary'
                                        ];
                                        $roleFrench = [
                                            'admin' => 'Administrateur',
                                            'accountant' => 'Comptable',
                                            'cement_manager' => 'Gestionnaire Ciment',
                                            'iron_manager' => 'Gestionnaire Fer',
                                            'cashier' => 'Caissier',
                                            'customer_service' => 'Service Client'
                                        ];
                                        $bgColor = $roleColors[$role->name] ?? 'bg-dark';
                                        $roleName = $roleFrench[$role->name] ?? $role->name;
                                    ?>
                                    <span class="badge <?php echo e($bgColor); ?> rounded-pill"><?php echo e($roleName); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Statut -->
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-toggle-on me-1"></i> Statut
                            </h6>
                            <?php if($user->is_active): ?>
                                <span class="badge bg-success rounded-pill">
                                    <i class="fas fa-check-circle me-1"></i> Actif
                                </span>
                            <?php else: ?>
                                <span class="badge bg-danger rounded-pill">
                                    <i class="fas fa-times-circle me-1"></i> Inactif
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- Informations supplémentaires -->
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>
                                Créé le <?php echo e($user->created_at->format('d/m/Y')); ?>

                            </small>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="card-footer bg-light border-0 p-3">
                        <div class="d-flex justify-content-between gap-2">
                            <a href="<?php echo e(route('admin.users.edit', $user)); ?>"
                               class="btn btn-sm btn-primary flex-fill"
                               title="Modifier">
                                <i class="fas fa-edit me-1"></i> Modifier
                            </a>

                            <form action="<?php echo e(route('admin.users.toggle-active', $user)); ?>"
                                  method="POST"
                                  class="flex-fill">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                        class="btn btn-sm <?php echo e($user->is_active ? 'btn-warning' : 'btn-success'); ?> w-100"
                                        title="<?php echo e($user->is_active ? 'Désactiver' : 'Activer'); ?>">
                                    <i class="fas <?php echo e($user->is_active ? 'fa-user-slash' : 'fa-user-check'); ?> me-1"></i>
                                    <?php echo e($user->is_active ? 'Désactiver' : 'Activer'); ?>

                                </button>
                            </form>

                            <button type="button"
                                    class="btn btn-sm btn-danger delete-user"
                                    data-id="<?php echo e($user->id); ?>"
                                    data-name="<?php echo e($user->name); ?>"
                                    title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Vue en tableau (cachée par défaut) -->
    <div id="tableView" class="users-view" style="display: none;">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>
                    Liste des Utilisateurs
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="usersTable">
                        <thead class="table-light">
                            <tr>
                                <th class="ps-3">N°</th>
                                <th>Utilisateur</th>
                                <th class="d-none d-md-table-cell">Email</th>
                                <th>Rôles</th>
                                <th class="text-center">Statut</th>
                                <th class="text-center">Date création</th>
                                <th class="text-end pe-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="searchable-table">
                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="user-row"
                                data-status="<?php echo e($user->is_active ? 'active' : 'inactive'); ?>"
                                data-roles="<?php echo e($user->roles->pluck('name')->implode(',')); ?>"
                                data-search="<?php echo e(strtolower($user->name . ' ' . $user->email)); ?>">
                                <td class="ps-3"><?php echo e($users->firstItem() + $index); ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle-sm me-2">
                                            <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                                        </div>
                                        <div>
                                            <div class="font-weight-bold"><?php echo e($user->name); ?></div>
                                            <div class="text-muted small d-md-none"><?php echo e($user->email); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell"><?php echo e($user->email); ?></td>
                                <td>
                                    <div class="d-flex flex-wrap gap-1">
                                        <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $roleColors = [
                                                    'admin' => 'bg-danger',
                                                    'accountant' => 'bg-primary',
                                                    'cement_manager' => 'bg-success',
                                                    'iron_manager' => 'bg-info',
                                                    'cashier' => 'bg-warning text-dark',
                                                    'customer_service' => 'bg-secondary'
                                                ];
                                                $roleFrench = [
                                                    'admin' => 'Administrateur',
                                                    'accountant' => 'Comptable',
                                                    'cement_manager' => 'Gestionnaire Ciment',
                                                    'iron_manager' => 'Gestionnaire Fer',
                                                    'cashier' => 'Caissier',
                                                    'customer_service' => 'Service Client'
                                                ];
                                                $bgColor = $roleColors[$role->name] ?? 'bg-dark';
                                                $roleName = $roleFrench[$role->name] ?? $role->name;

                                                // Version courte pour mobile
                                                $shortName = [
                                                    'Administrateur' => 'Admin',
                                                    'Comptable' => 'Compt.',
                                                    'Gestionnaire Ciment' => 'Gest. Cim.',
                                                    'Gestionnaire Fer' => 'Gest. Fer',
                                                    'Caissier' => 'Caiss.',
                                                    'Service Client' => 'Serv. Cl.'
                                                ];
                                            ?>
                                            <span class="badge <?php echo e($bgColor); ?> d-none d-md-inline-block"><?php echo e($roleName); ?></span>
                                            <span class="badge <?php echo e($bgColor); ?> d-inline-block d-md-none"><?php echo e($shortName[$roleName] ?? $roleName); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <?php if($user->is_active): ?>
                                        <span class="badge bg-success rounded-pill">Actif</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger rounded-pill">Inactif</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <small class="text-muted"><?php echo e($user->created_at->format('d/m/Y')); ?></small>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-1 pe-3">
                                        <a href="<?php echo e(route('admin.users.edit', $user)); ?>"
                                           class="btn btn-sm btn-primary"
                                           title="Modifier"
                                           data-bs-toggle="tooltip">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <form action="<?php echo e(route('admin.users.toggle-active', $user)); ?>"
                                              method="POST"
                                              class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                    class="btn btn-sm <?php echo e($user->is_active ? 'btn-warning' : 'btn-success'); ?>"
                                                    title="<?php echo e($user->is_active ? 'Désactiver' : 'Activer'); ?>"
                                                    data-bs-toggle="tooltip">
                                                <i class="fas <?php echo e($user->is_active ? 'fa-user-slash' : 'fa-user-check'); ?>"></i>
                                            </button>
                                        </form>

                                        <button type="button"
                                                class="btn btn-sm btn-danger delete-user"
                                                data-id="<?php echo e($user->id); ?>"
                                                data-name="<?php echo e($user->name); ?>"
                                                title="Supprimer"
                                                data-bs-toggle="tooltip">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-4">
        <?php echo e($users->links()); ?>

    </div>

    <!-- Modal de confirmation de suppression -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        Confirmer la suppression
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong id="userName"></strong> ?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Cette action est irréversible !
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <form id="deleteForm" method="POST" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Supprimer
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Styles généraux */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    .bg-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .bg-gradient-info {
        background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
    }
    .bg-gradient-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }

    /* Bordures colorées pour les cartes de statistiques */
    .border-left-primary {
        border-left: 4px solid #4e73df !important;
    }
    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }
    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }

    /* Cartes d'utilisateurs */
    .user-card {
        transition: all 0.3s ease;
        border-radius: 15px;
        overflow: hidden;
    }
    .user-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Avatar circulaire */
    .avatar-circle {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: bold;
        font-size: 0.875rem;
        text-transform: uppercase;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .avatar-circle-sm {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: bold;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    /* Indicateur de statut */
    .status-indicator {
        position: relative;
    }
    .status-indicator i {
        font-size: 0.75rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* Amélioration des badges */
    .badge {
        font-size: 0.75rem;
        padding: 0.5em 0.75em;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    .badge:hover {
        transform: scale(1.05);
    }

    /* Champs de formulaire flottants */
    .form-floating > .form-control,
    .form-floating > .form-select {
        border: 2px solid #e2e8f0;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
    }
    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    .btn:hover {
        transform: translateY(-1px);
    }

    /* Animation pour les cartes */
    .user-item {
        animation: slideInUp 0.6s ease-out;
    }
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Styles pour le tableau */
    .table th {
        font-weight: 600;
        border-top: none;
        background: #f8f9fc;
        color: #5a5c69;
    }
    .table td {
        vertical-align: middle;
        border-top: 1px solid #e3e6f0;
    }
    .table-hover tbody tr:hover {
        background-color: #f8f9fc;
        transform: translateX(3px);
        transition: all 0.2s ease;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .user-card {
            margin-bottom: 1rem;
        }
        .avatar-circle {
            width: 35px;
            height: 35px;
            font-size: 0.75rem;
        }
        .card-body {
            padding: 1rem;
        }
    }

    /* Styles pour les filtres */
    .card-header {
        background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
        border-bottom: 1px solid #e3e6f0;
    }

    /* Animation de chargement */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Styles pour les statistiques */
    .text-xs {
        font-size: 0.7rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Amélioration des modales */
    .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }
    .modal-header {
        border-bottom: 1px solid #e3e6f0;
        background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
    }

    /* Pagination personnalisée */
    .pagination {
        border-radius: 0.5rem;
    }
    .page-link {
        border: none;
        color: #5a5c69;
        font-weight: 600;
        margin: 0 2px;
        border-radius: 0.5rem;
    }
    .page-link:hover {
        background-color: #4e73df;
        color: white;
    }
    .page-item.active .page-link {
        background-color: #4e73df;
        border-color: #4e73df;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments du DOM
    const toggleViewBtn = document.getElementById('toggleView');
    const viewIcon = document.getElementById('viewIcon');
    const cardView = document.getElementById('cardView');
    const tableView = document.getElementById('tableView');
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const roleFilter = document.getElementById('roleFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const deleteForm = document.getElementById('deleteForm');

    // État de la vue (cartes par défaut)
    let isCardView = true;

    // Initialiser les tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Basculer entre vue cartes et tableau
    toggleViewBtn.addEventListener('click', function() {
        isCardView = !isCardView;

        if (isCardView) {
            cardView.style.display = 'block';
            tableView.style.display = 'none';
            viewIcon.className = 'fas fa-th-large';
            toggleViewBtn.title = 'Vue tableau';
        } else {
            cardView.style.display = 'none';
            tableView.style.display = 'block';
            viewIcon.className = 'fas fa-table';
            toggleViewBtn.title = 'Vue cartes';
        }

        // Réappliquer les filtres après changement de vue
        applyFilters();
    });

    // Fonction de filtrage
    function applyFilters() {
        const searchText = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const roleValue = roleFilter.value;

        // Filtrer les cartes
        const userItems = document.querySelectorAll('.user-item');
        userItems.forEach(item => {
            const searchData = item.dataset.search;
            const statusData = item.dataset.status;
            const rolesData = item.dataset.roles;

            let showItem = true;

            // Filtre de recherche
            if (searchText && !searchData.includes(searchText)) {
                showItem = false;
            }

            // Filtre de statut
            if (statusValue) {
                if (statusValue === 'active' && statusData !== 'active') {
                    showItem = false;
                }
                if (statusValue === 'inactive' && statusData !== 'inactive') {
                    showItem = false;
                }
            }

            // Filtre de rôle
            if (roleValue && !rolesData.includes(roleValue)) {
                showItem = false;
            }

            item.style.display = showItem ? 'block' : 'none';
        });

        // Filtrer les lignes du tableau
        const userRows = document.querySelectorAll('.user-row');
        userRows.forEach(row => {
            const searchData = row.dataset.search;
            const statusData = row.dataset.status;
            const rolesData = row.dataset.roles;

            let showRow = true;

            // Filtre de recherche
            if (searchText && !searchData.includes(searchText)) {
                showRow = false;
            }

            // Filtre de statut
            if (statusValue) {
                if (statusValue === 'active' && statusData !== 'active') {
                    showRow = false;
                }
                if (statusValue === 'inactive' && statusData !== 'inactive') {
                    showRow = false;
                }
            }

            // Filtre de rôle
            if (roleValue && !rolesData.includes(roleValue)) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        });

        // Mettre à jour le compteur de résultats
        updateResultsCounter();
    }

    // Mettre à jour le compteur de résultats
    function updateResultsCounter() {
        const visibleItems = isCardView
            ? document.querySelectorAll('.user-item[style="display: block"], .user-item:not([style*="display: none"])')
            : document.querySelectorAll('.user-row[style=""], .user-row:not([style*="display: none"])');

        // Vous pouvez ajouter un élément pour afficher le nombre de résultats
        console.log(`${visibleItems.length} utilisateur(s) affiché(s)`);
    }

    // Effacer tous les filtres
    function clearAllFilters() {
        searchInput.value = '';
        statusFilter.value = '';
        roleFilter.value = '';
        applyFilters();
    }

    // Gestionnaires d'événements pour les filtres
    searchInput.addEventListener('input', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    roleFilter.addEventListener('change', applyFilters);
    clearFiltersBtn.addEventListener('click', clearAllFilters);

    // Gestionnaire pour les boutons de suppression
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-user')) {
            const button = e.target.closest('.delete-user');
            const userId = button.dataset.id;
            const userName = button.dataset.name;

            document.getElementById('userName').textContent = userName;
            deleteForm.action = `/admin/users/${userId}`;
            deleteModal.show();
        }
    });

    // Animation d'entrée pour les cartes
    function animateCards() {
        const cards = document.querySelectorAll('.user-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    // Fonction de recherche en temps réel avec debounce
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 300);
    });

    // Gestion des formulaires de changement de statut
    document.addEventListener('submit', function(e) {
        if (e.target.matches('form[action*="toggle-active"]')) {
            e.preventDefault();

            const form = e.target;
            const button = form.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            // Afficher un spinner
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            // Soumettre le formulaire
            fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new FormData(form)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Recharger la page pour mettre à jour l'affichage
                    window.location.reload();
                } else {
                    throw new Error(data.message || 'Erreur lors de la mise à jour');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                button.innerHTML = originalText;
                button.disabled = false;

                // Afficher une notification d'erreur
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur !',
                        text: error.message || 'Une erreur est survenue lors de la mise à jour du statut.',
                    });
                }
            });
        }
    });

    // Initialisation
    animateCards();
    applyFilters();

    // Exposer la fonction clearAllFilters globalement
    window.clearAllFilters = clearAllFilters;
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/index.blade.php ENDPATH**/ ?>